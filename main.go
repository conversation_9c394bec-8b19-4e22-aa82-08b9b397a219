package main

import (
	"context"
	"crypto/ed25519"
	"crypto/sha256"
	"crypto/tls"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/http_client"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"gopkg.in/twindagger/httpsig.v1"
)

const (
	JumpServerHost       = "https://sec-jumpserver.yorkapp.com"
	CommandLogApi        = "/api/v1/terminal/commands/?command_storage_id=%s&order=%s&date_from=%s&date_to=%s&offset=%d&limit=%d"
	LoginLogApi          = "/api/v1/audits/login-logs/?order=%s&date_from=%s&date_to=%s&offset=%d&limit=%d"
	PasswordChangeLogApi = "/api/v1/audits/password-change-logs/?order=%s&date_from=%s&date_to=%s&offset=%d&limit=%d"
	OperateLogApi        = "/api/v1/audits/operate-logs/?order=%s&date_from=%s&date_to=%s&offset=%d&limit=%d"
	AccessKey            = "xxx"
	AccessSecret         = "xxx"
	CommandStorageID     = "fd1bd8de-080a-45c1-ba26-44f2163d1670" // 命令存储的storageID，右上角设置->存储设置->命令存储
)

func Sign(r *http.Request) error {
	headers := []string{"(request-target)", "date"}
	signer, err := httpsig.NewRequestSigner(AccessKey, AccessSecret, "hmac-sha256")
	if err != nil {
		return err
	}
	return signer.SignRequest(r, headers, nil)
}

type Result struct {
	Count    int64         `json:"count"`
	Next     *string       `json:"next"`
	Previous *string       `json:"previous"`
	Results  []interface{} `json:"results"`
}

func DoGetRequest(method, api string) (res []byte, err error) {
	req, err := http.NewRequest(method, fmt.Sprintf("%s%s", JumpServerHost, api), nil)
	if err != nil {
		return nil, err
	}
	if err = Sign(req); err != nil {
		return nil, err
	}
	httpResp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer httpResp.Body.Close()
	return io.ReadAll(httpResp.Body)
}

// GetCommands 获取terminal command日志
// Parameters:
//   - from: 起始毫秒时间戳
//   - to: 结束毫秒时间戳
func GetCommands(from, to int64) (logs []interface{}, err error) {
	dateFrom := url.QueryEscape(time.UnixMilli(from).Format("2006-01-02T15:04:05.999Z07:00"))
	dateTo := url.QueryEscape(time.UnixMilli(to).Format("2006-01-02T15:04:05.999Z07:00"))
	for offset, limit := 0, 50; ; offset += limit {
		api := fmt.Sprintf(CommandLogApi, CommandStorageID, "-timestamp", dateFrom, dateTo, offset, limit)
		body, err := DoGetRequest(http.MethodGet, api)
		if err != nil {
			return logs, err
		}
		res := &Result{}
		if err := json.Unmarshal(body, res); err != nil {
			return logs, err
		}
		logs = append(logs, res.Results...)
		if len(res.Results) < limit {
			break
		}
	}
	return logs, nil
}

// GetLoginLogs 获取login日志
// Parameters:
//   - from: 起始毫秒时间戳
//   - to: 结束毫秒时间戳
func GetLoginLogs(from, to int64) (logs []interface{}, err error) {
	dateFrom := url.QueryEscape(time.UnixMilli(from).Format("2006-01-02T15:04:05.999Z07:00"))
	dateTo := url.QueryEscape(time.UnixMilli(to).Format("2006-01-02T15:04:05.999Z07:00"))
	for offset, limit := 0, 50; ; offset += limit {
		api := fmt.Sprintf(LoginLogApi, "-timestamp", dateFrom, dateTo, offset, limit)
		body, err := DoGetRequest(http.MethodGet, api)
		if err != nil {
			return logs, err
		}
		res := &Result{}
		if err := json.Unmarshal(body, res); err != nil {
			return logs, err
		}
		logs = append(logs, res.Results...)
		if len(res.Results) < limit {
			break
		}
	}
	return logs, nil
}

// GetPasswordChangeLogs 获取改密日志
// Parameters:
//   - from: 起始毫秒时间戳
//   - to: 结束毫秒时间戳
func GetPasswordChangeLogs(from, to int64) (logs []interface{}, err error) {
	dateFrom := url.QueryEscape(time.UnixMilli(from).Format("2006-01-02T15:04:05.999Z07:00"))
	dateTo := url.QueryEscape(time.UnixMilli(to).Format("2006-01-02T15:04:05.999Z07:00"))
	for offset, limit := 0, 50; ; offset += limit {
		api := fmt.Sprintf(PasswordChangeLogApi, "-timestamp", dateFrom, dateTo, offset, limit)
		body, err := DoGetRequest(http.MethodGet, api)
		if err != nil {
			return logs, err
		}
		res := &Result{}
		if err := json.Unmarshal(body, res); err != nil {
			return logs, err
		}
		logs = append(logs, res.Results...)
		if len(res.Results) < limit {
			break
		}
	}
	return logs, nil
}

// GetOperateLogs 获取改密日志
// Parameters:
//   - from: 起始毫秒时间戳
//   - to: 结束毫秒时间戳
func GetOperateLogs(from, to int64) (logs []interface{}, err error) {
	dateFrom := url.QueryEscape(time.UnixMilli(from).Format("2006-01-02T15:04:05.999Z07:00"))
	dateTo := url.QueryEscape(time.UnixMilli(to).Format("2006-01-02T15:04:05.999Z07:00"))
	for offset, limit := 0, 50; ; offset += limit {
		api := fmt.Sprintf(OperateLogApi, "-timestamp", dateFrom, dateTo, offset, limit)
		body, err := DoGetRequest(http.MethodGet, api)
		if err != nil {
			return logs, err
		}
		res := &Result{}
		if err := json.Unmarshal(body, res); err != nil {
			return logs, err
		}
		logs = append(logs, res.Results...)
		if len(res.Results) < limit {
			break
		}
	}
	return logs, nil
}

type TriggerFlowRequest struct {
	FlowName       string      `json:"flowName" validate:"required"`
	FlowInstanceID string      `json:"flowInstanceID" validate:"required"`
	NodeName       string      `json:"nodeName" validate:"required"`
	Pass           bool        `json:"pass"`
	Data           interface{} `json:"data"`
}

type UpdateData struct {
	Id        string `json:"id"`
	Amount    string `json:"amount"`
	ChainId   string `json:"chainId"`
	FeeAmount string `json:"feeAmount"`
	ToAddress string `json:"toAddress"`
}

// Your initialization function
func main() {
	rawUrl := "https://audit-open-api.sec-test.yorkapp.com/api/flow/trigger"
	triggerReq := &TriggerFlowRequest{
		FlowName:       "withdraw_order",
		FlowInstanceID: "123456",
		NodeName:       "mgt_result",
		Pass:           true,
		Data: UpdateData{
			Id:        "1",
			Amount:    "0.1",
			ChainId:   "trc20",
			FeeAmount: "0.01",
			ToAddress: "abcde",
		},
	}
	body, err := common.JsonStringEncode(triggerReq)
	if err != nil {
		panic(err)
	}
	client := http_client.NewHttpClient(common.GetLogger(context.Background()), true).
		SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	req := client.R()
	signHeaders, err := GenerateSignature(context.Background(), apiKey, apiSecret, http.MethodPost, rawUrl, body)
	if err != nil {
		panic(err)
	}
	resp, err := req.SetHeaders(signHeaders).SetBody(triggerReq).Post(rawUrl)
	if err != nil {
		panic(err)
	} else if resp.StatusCode() != http.StatusOK {
		panic(common.ErrHttpResponsef(resp.Status()))
	}
	fmt.Println(string(resp.Body()))
}

const (
	apiKey    = "ca1ad2b97b34521ebd3746a6d0b16aea9e7e7053c553d84a481ce6819355f585"
	apiSecret = "58814a633ae9f70fdd5d260f737ffcdd63db843be63060864e140cffc3cc83ae"
)

func GenerateSignature(ctx context.Context, apiKey, apiSecret, method, rawUrl string, requestBody string) (headers map[string]string, err error) {
	// 解码私钥
	privateKey, err := hex.DecodeString(apiSecret)
	if err != nil {
		return nil, err
	}

	u, err := url.Parse(rawUrl)
	if err != nil {
		return nil, err
	}
	// 构造请求体
	timestamp := fmt.Sprintf("%d", time.Now().UnixMilli())

	message := fmt.Sprintf("%s|%s|%s|%s|%s", strings.ToUpper(method), u.Path, timestamp, u.Query().Encode(), requestBody)

	// 计算两次 SHA256 哈希
	hash1 := sha256.Sum256([]byte(message))
	hash2 := sha256.Sum256(hash1[:])
	doubleHash := hash2[:]

	// 使用私钥签名
	signature := ed25519.Sign(ed25519.NewKeyFromSeed(privateKey), doubleHash)
	signatureHex := hex.EncodeToString(signature)

	// 设置请求头
	headers = make(map[string]string)
	headers["X-Api-Signature"] = signatureHex
	headers["X-Api-Timestamp"] = timestamp
	headers["X-Api-Key"] = apiKey

	return headers, nil
}
