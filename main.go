package main

import (
	"bytes"
	"context"
	"crypto/ed25519"
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"strings"
	"time"
)

const (
	pubKey     = "7a9d139499cc76e4a777d07e4eddbb25252dd7c9017e15464975a1cc42110cff"
	privateKey = "e38efa909191da2db2edfbd5d9acab8a32b3d90b1de088cf03f6836a23c73d4c"
)

func GenerateSignature(ctx context.Context, apiKey, apiSecret, method, rawUrl string, requestBody string) (headers map[string]string, err error) {
	// 解码私钥
	privateKey, err := hex.DecodeString(apiSecret)
	if err != nil {
		return nil, err
	}

	u, err := url.Parse(rawUrl)
	if err != nil {
		return nil, err
	}
	// 构造请求体
	timestamp := fmt.Sprintf("%d", time.Now().UnixMilli())

	message := fmt.Sprintf("%s|%s|%s|%s|%s", strings.ToUpper(method), u.Path, timestamp, u.Query().Encode(), requestBody)

	// 计算两次 SHA256 哈希
	hash1 := sha256.Sum256([]byte(message))
	hash2 := sha256.Sum256(hash1[:])
	doubleHash := hash2[:]

	// 使用私钥签名

	signature := ed25519.Sign(ed25519.NewKeyFromSeed(privateKey), doubleHash)
	signatureHex := hex.EncodeToString(signature)

	// 设置请求头
	headers = make(map[string]string)
	headers["X-Api-Signature"] = signatureHex
	headers["X-Api-Timestamp"] = timestamp
	headers["X-Api-Key"] = apiKey

	return headers, nil
}

// GenerateKey 生成ed25519密钥对并以16进制编码打印
func GenerateKey() {
	// 生成ed25519密钥对
	publicKey, privateKey, err := ed25519.GenerateKey(rand.Reader)
	if err != nil {
		log.Fatalf("生成密钥失败: %v", err)
	}

	// 将密钥转换为16进制编码
	publicKeyHex := hex.EncodeToString(publicKey)
	privateKeyHex := hex.EncodeToString(privateKey.Seed())

	// 打印密钥
	fmt.Println("=== Ed25519 密钥对 ===")
	fmt.Printf("公钥 (Public Key): %s\n", publicKeyHex)
	fmt.Printf("私钥 (Private Key): %s\n", privateKeyHex)
	fmt.Println()

	// 打印密钥长度信息
	fmt.Printf("公钥长度: %d 字节 (%d 十六进制字符)\n", len(publicKey), len(publicKeyHex))
	fmt.Printf("私钥长度: %d 字节 (%d 十六进制字符)\n", len(privateKey), len(privateKeyHex))
	fmt.Println()

	// 验证密钥对是否有效
	testMessage := []byte("test message for verification")
	signature := ed25519.Sign(privateKey, testMessage)
	isValid := ed25519.Verify(publicKey, testMessage, signature)

	fmt.Printf("密钥对验证: %v\n", isValid)
	if isValid {
		fmt.Println("✅ 密钥对生成成功且有效!")
	} else {
		fmt.Println("❌ 密钥对验证失败!")
	}
}

// FlowTriggerRequest 流程触发请求结构体
type FlowTriggerRequest struct {
	FlowName       string      `json:"flowName" validate:"required"`
	FlowInstanceID string      `json:"flowInstanceID"` // 当不为0时，表示继续流程，为0时表示发起新流程
	NodeName       string      `json:"nodeName" validate:"required"`
	Data           interface{} `json:"data"`
}

// TriggerFlow 触发流程API
func TriggerFlow(apiKey, apiSecret string, request FlowTriggerRequest) error {
	// API端点
	apiURL := "http://127.0.0.1:10001/api/flow/trigger"

	// 将请求结构体转换为JSON
	requestBody, err := json.Marshal(request)
	if err != nil {
		return fmt.Errorf("序列化请求体失败: %v", err)
	}

	// 生成签名
	ctx := context.Background()
	headers, err := GenerateSignature(ctx, apiKey, apiSecret, "POST", apiURL, string(requestBody))
	if err != nil {
		return fmt.Errorf("生成签名失败: %v", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", apiURL, bytes.NewBuffer(requestBody))
	if err != nil {
		return fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	// 打印请求信息
	fmt.Println("=== 请求信息 ===")
	fmt.Printf("URL: %s\n", apiURL)
	fmt.Printf("Method: POST\n")
	fmt.Printf("Request Body: %s\n", string(requestBody))
	fmt.Println("Headers:")
	for key, value := range headers {
		fmt.Printf("  %s: %s\n", key, value)
	}
	fmt.Println()

	// 发送请求
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %v", err)
	}

	// 打印响应信息
	fmt.Println("=== 响应信息 ===")
	fmt.Printf("Status Code: %d\n", resp.StatusCode)
	fmt.Printf("Status: %s\n", resp.Status)
	fmt.Printf("Response Body: %s\n", string(responseBody))

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(responseBody))
	}

	fmt.Println("✅ 流程触发成功!")
	return nil
}

// Your initialization function
func main() {
	message := "%E4%BA%A4%E6%98%93%E6%89%80-%E4%BA%A4%E6%98%93%E9%A3%8E%E6%8E%A7%E7%AE%A1%E7%90%86-ADL%E7%AE%A1%E7%90%86-ADL%E9%A2%84%E8%AD%A6%E5%92%8C%E8%A7%A6%E5%8F%91%E6%9C%BA%E5%88%B6"
	v, err := url.PathUnescape(message)
	if err != nil {
		panic(err)
	}
	fmt.Println(v)
}
